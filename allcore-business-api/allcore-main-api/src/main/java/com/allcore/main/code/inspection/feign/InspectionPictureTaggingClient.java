package com.allcore.main.code.inspection.feign;


import com.allcore.common.constant.LauncherConstant;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.inspection.dto.InspectionTaskPicQueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name= LauncherConstant.MAIN_SERVER_NAME, path = "/inspectionpicturetagging")
public interface InspectionPictureTaggingClient {


        @PostMapping("/pvRemoveDefectDetailInfo")
        public R getDefectDetailInfo(@RequestBody InspectionTaskPicQueryDTO dto);



}
