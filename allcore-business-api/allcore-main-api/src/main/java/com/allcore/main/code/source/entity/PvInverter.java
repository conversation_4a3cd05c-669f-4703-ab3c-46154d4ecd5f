package com.allcore.main.code.source.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Size;

/**
 * @program: bl
 * @description: 光伏逆变器实体类
 * @author: fanxiang
 * @create: 2025-05-22 15:25
 **/

@Data
@TableName("main_inverter_info")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="PvArea实体")
public class PvInverter extends ZxhcEntity {

    @Size(min=1,max=30,message = "设备名称长度范围 1-30")
    @ApiModelProperty("设备名称")
    private String deviceName;

    @Size(min=1,max=30,message = "逆变器型号长度范围 1-30")
    @ApiModelProperty("逆变器型号")
    private String inverterModel;

    @Size(min=1,max=30,message = "逆变器编号长度范围 1-30")
    @ApiModelProperty("逆变器编号")
    private String inverterNumber;

    @Size(min=1,max=30,message = "逆变器序列号长度范围 1-30")
    @ApiModelProperty("逆变器序列号")
    private String inverterSerialNumber;

    @DecimalMin(value = "-180", message = "经度最小值 -180")
    @DecimalMax(value = "180", message = "经度最大值 180")
    @ApiModelProperty(value = "经度")
    private String longitude;

    @DecimalMin(value = "-90", message = "纬度最小值为 -90")
    @DecimalMax(value = "90", message = "纬度最大值90")
    @ApiModelProperty("纬度")
    private String latitude;

    @ApiModelProperty("高程")
    private String height;

    @ApiModelProperty("关联组串")
    @TableField(exist = false)
    private String attachedString;

    @ApiModelProperty("额定功率(MW)")
    private String ratedPower;

    @ApiModelProperty("投入使用时间")
    private String usageTime;

    @ApiModelProperty("生成厂家")
    private String manufacturer;

    @TableField(exist = false)
    private String deptCodeZh;

    @ApiModelProperty("光伏区域id")
    private String pvAreaId;
}
